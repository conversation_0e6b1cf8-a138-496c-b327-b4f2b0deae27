{#
/**
 * @file
 * Theme implementation to display a menu with fields.
 *
 * It is a very similar implementation to the system module template
 * except for the following code that will render the menu item
 * as an entity when is available.
 *
 * @code
 * {% if item.content %}
 *   {{ item.content }}
 * {% else %}
 *   {{ link(item.title, item.url) }}
 * {% endif %}
 * @endcode
 *
 * When the menu.html.twig template is overriden in your theme
 * this piece of code needs to be added.
 */
#}
{% import _self as menus %}

{#
We call a macro which calls itself to render the full tree.
@see https://twig.symfony.com/doc/1.x/tags/macro.html
#}
{{ menus.menu_links(items, attributes, 0) }}

{% macro menu_links(items, attributes, menu_level, submenu_background_color) %}
  {% import _self as menus %}
  {% if items %}
    {% if menu_level == 0 %}
      {% set attributes = attributes.addClass('main-menu dropdown menu') %}
      <ul{{ attributes }} data-dropdown-menu>
    {% else %}
      <ul class="menu" {{ submenu_background_color }}>
    {% endif %}
    {% for item in items %}
      {% set rootClass = menu_level == 0 ? 'active-root-item' : 'is-active' %}
      {% set color = item.content['#menu_link_content'].field_submenu_background.value.0.color %}
      {% set opacity = item.content['#menu_link_content'].field_submenu_background.value.0.opacity %}
      {% set submenu_background_color = menu_level == 0 ? 'style=background-color:' ~ color ~ ';opacity:' ~ opacity ~ ';' : '' %}
      {% set item_attributes = item.attributes.addClass((item.in_active_trail ? rootClass : '')) %}
      {% if menu_level == 0 and color %}
        {% set hash = 'link_' ~ random() %}
        <style nonce="{{ csp_nonce }}">
          #{{ hash }} a:hover, #{{ hash }} ul a, #{{ hash }}.is-active  a {background:{{ color }} !important; opacity:{{ opacity }} !important;}
          #{{ hash }} span:hover, #{{ hash }} ul span, #{{ hash }}.is-active span {background:{{ color }} !important; opacity:{{ opacity }} !important;}
        </style>
      {% endif %}
      <li id="{{ hash }}" {{ item_attributes }}>
        {{ link(item.title, item.url) }}
        {% if item.below %}
          {{ menus.menu_links(item.below, attributes, menu_level + 1, submenu_background_color) }}
        {% endif %}
      </li>
    {% endfor %}
    </ul>
  {% endif %}
{% endmacro %}
